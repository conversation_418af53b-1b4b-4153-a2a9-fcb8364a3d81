"""
邮件获取核心函数使用示例

这个文件展示了如何使用 get_emails_core 函数来获取邮件数据，
该函数已经移除了所有计费相关的逻辑，专门用于内部调用。
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from routes.email import get_emails_core


async def example_usage():
    """使用示例"""
    
    # 示例1: 基本使用
    print("=== 示例1: 基本邮件获取 ===")
    result = await get_emails_core(
        email="<EMAIL>",
        num=5
    )
    
    print(f"状态: {result['status']}")
    print(f"消息: {result['message']}")
    if result['data']:
        print(f"获取到 {len(result['data'])} 封邮件")
        # 显示第一封邮件的基本信息
        if result['data']:
            first_email = result['data'][0]
            print(f"第一封邮件:")
            print(f"  - 发件人: {first_email.get('From', 'N/A')}")
            print(f"  - 主题: {first_email.get('Subject', 'N/A')}")
            print(f"  - 时间: {first_email.get('FormatTime', 'N/A')}")
    print()
    
    # 示例2: 带用户名和主机信息的使用
    print("=== 示例2: 带额外参数的邮件获取 ===")
    result = await get_emails_core(
        email="<EMAIL>",
        num=3,
        username="test_user",
        request_host="api.example.com"
    )
    
    print(f"状态: {result['status']}")
    print(f"消息: {result['message']}")
    print()
    
    # 示例3: 错误处理
    print("=== 示例3: 错误处理示例 ===")
    result = await get_emails_core(
        email="invalid_email",
        num=1
    )
    
    print(f"状态: {result['status']}")
    print(f"消息: {result['message']}")
    print()


async def batch_email_processing():
    """批量邮件处理示例"""
    print("=== 批量邮件处理示例 ===")
    
    email_list = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>"
    ]
    
    tasks = []
    for email in email_list:
        task = get_emails_core(email=email, num=2, username="batch_user")
        tasks.append(task)
    
    # 并发执行所有邮件获取任务
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"邮箱 {email_list[i]} 处理失败: {result}")
        else:
            print(f"邮箱 {email_list[i]}: {result['message']}")


def process_email_data(email_data):
    """处理邮件数据的辅助函数"""
    if not email_data or not email_data.get('data'):
        return []
    
    processed_emails = []
    for email in email_data['data']:
        processed_email = {
            'sender': email.get('From', ''),
            'subject': email.get('Subject', ''),
            'content': email.get('Content', ''),
            'formatted_time': email.get('FormatTime', ''),
            'timestamp': email.get('Time', 0)
        }
        processed_emails.append(processed_email)
    
    return processed_emails


async def advanced_usage_example():
    """高级使用示例"""
    print("=== 高级使用示例 ===")
    
    # 获取邮件数据
    result = await get_emails_core(
        email="<EMAIL>",
        num=10,
        username="advanced_user"
    )
    
    if result['status']:
        # 处理邮件数据
        processed_emails = process_email_data(result)
        
        print(f"处理了 {len(processed_emails)} 封邮件")
        
        # 按时间排序
        processed_emails.sort(key=lambda x: x['timestamp'], reverse=True)
        
        # 显示最新的3封邮件
        print("最新的3封邮件:")
        for i, email in enumerate(processed_emails[:3]):
            print(f"  {i+1}. {email['subject']} - {email['formatted_time']}")
    else:
        print(f"获取邮件失败: {result['message']}")


if __name__ == "__main__":
    print("邮件获取核心函数使用示例\n")
    
    # 运行基本示例
    asyncio.run(example_usage())
    
    # 运行批量处理示例
    asyncio.run(batch_email_processing())
    
    # 运行高级使用示例
    asyncio.run(advanced_usage_example())
    
    print("示例运行完成!")
