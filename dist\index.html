<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>可达鸭API</title>
  <meta name='description' content='可达鸭API聚合平台，提供临时邮箱、AI对话、极验验证码识别、二维码生成与解析等多种高效API接口，支持分类筛选、点数计费，接口文档详细，持续更新更多实用API，助力开发者高效集成与调用。'>
  <meta name="keywords" content="API聚合平台,验证码识别,临时邮箱,AI对话,二维码生成,可达鸭API,接口服务,开发者工具">
  <meta name="author" content="可达鸭API">
  <meta property="og:title" content="可达鸭API聚合平台 - 验证码识别、临时邮箱、AI对话、二维码生成服务">
  <meta property="og:description" content="可达鸭API聚合平台，提供临时邮箱、AI对话、极验验证码识别、二维码生成与解析等多种高效API接口">
  <meta property="og:type" content="website">
  <meta property="og:image" content="/favicon.svg">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="可达鸭API聚合平台 - 验证码识别、临时邮箱、AI对话、二维码生成服务">
  <meta name="twitter:description" content="可达鸭API聚合平台，提供临时邮箱、AI对话、极验验证码识别、二维码生成与解析等多种高效API接口">
  <meta name="twitter:image" content="/favicon.svg">
  <!-- 网站图标 -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg">
  <link rel="alternate icon" href="/favicon.svg">
  <link rel="mask-icon" href="/favicon.svg" color="#000000">

  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">

  <!-- 结构化数据 -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "可达鸭API聚合平台",
    "description": "提供验证码识别、临时邮箱、AI对话、二维码生成等多种API接口服务的聚合平台",
    "url": "https://www.kedaya.xyz",
    "applicationCategory": "DeveloperApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "description": "API接口服务",
      "priceCurrency": "CNY"
    },
    "provider": {
      "@type": "Organization",
      "name": "可达鸭API"
    },
    "featureList": [
      "验证码识别API",
      "临时邮箱API",
      "AI对话API",
      "二维码生成API",
      "二维码解析API"
    ]
  }
  </script>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Inter', system-ui, -apple-system, sans-serif;
    }

    html,
    body {
      margin: 0;
      padding: 0;
      min-height: 100vh;
      background-color: #f8f9fa;
      transition: background-color 0.3s ease;
      /* 添加过渡效果 */
    }

    html.dark-theme,
    html.dark-theme body {
      background-color: #111827;
    }

    /* 确保文本在亮色和暗色主题下都有足够的对比度 */
    body {
      color: #111827;
    }

    html.dark-theme body {
      color: #f9fafb;
    }
  </style>
  <script>
    // 在页面加载时检查主题并立即应用，避免闪烁
    (function () {
      try {
        if (typeof window === 'undefined' || !window.localStorage) return;

        const savedTheme = window.localStorage.getItem('theme') || 'system';
        let isDark = false;

        if (savedTheme === 'dark') {
          isDark = true;
        } else if (savedTheme === 'system') {
          isDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        }

        // 如果是暗黑模式，立即添加类
        if (isDark) {
          document.documentElement.classList.add('dark-theme');
        }
      } catch (e) {
        console.error('Error initializing theme', e);
      }
    })();
  </script>
  <script type="module" crossorigin src="/assets/index-3f884025.js"></script>
  <link rel="stylesheet" href="/assets/index-0d216bcf.css">
</head>

<body>
  <div id="app"></div>
  
</body>

</html>