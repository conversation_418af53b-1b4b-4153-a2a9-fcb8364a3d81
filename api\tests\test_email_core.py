"""
邮件获取核心函数的测试文件

测试 get_emails_core 函数的各种场景
"""

import pytest
import asyncio
import sys
import os
from unittest.mock import patch, AsyncMock, MagicMock

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from routes.email import get_emails_core


class TestEmailCore:
    """邮件核心函数测试类"""

    @pytest.mark.asyncio
    async def test_successful_email_retrieval(self):
        """测试成功获取邮件"""
        # 模拟邮件数据
        mock_emails = [
            {
                "From": "<EMAIL>",
                "Subject": "Test Email 1",
                "Content": "This is test content 1",
                "Time": "1640995200"  # 2022-01-01 00:00:00 UTC
            },
            {
                "From": "<EMAIL>", 
                "Subject": "Test Email 2",
                "Content": "This is test content 2",
                "Time": "1640995260"  # 2022-01-01 00:01:00 UTC
            }
        ]
        
        with patch('routes.email.get_emails_async', new_callable=AsyncMock) as mock_async:
            mock_async.return_value = mock_emails
            
            result = await get_emails_core("<EMAIL>", 2)
            
            assert result["status"] is True
            assert len(result["data"]) == 2
            assert "成功获取到 2 封邮件" in result["message"]
            
            # 检查时间格式化
            for email in result["data"]:
                assert "FormatTime" in email
                assert email["FormatTime"] != "时间格式错误"

    @pytest.mark.asyncio
    async def test_async_fallback_to_sync(self):
        """测试异步方法失败时回退到同步方法"""
        mock_emails = [{"From": "<EMAIL>", "Subject": "Test", "Time": "1640995200"}]
        
        with patch('routes.email.get_emails_async', new_callable=AsyncMock) as mock_async, \
             patch('routes.email.get_emails') as mock_sync:
            
            # 异步方法抛出异常
            mock_async.side_effect = Exception("Async failed")
            mock_sync.return_value = mock_emails
            
            result = await get_emails_core("<EMAIL>", 1)
            
            assert result["status"] is True
            assert len(result["data"]) == 1
            mock_async.assert_called_once()
            mock_sync.assert_called_once()

    @pytest.mark.asyncio
    async def test_unregistered_email(self):
        """测试未注册的邮箱"""
        with patch('routes.email.get_emails_async', new_callable=AsyncMock) as mock_async:
            mock_async.return_value = {"msg": "未注册"}
            
            result = await get_emails_core("<EMAIL>", 1)
            
            assert result["status"] is False
            assert result["data"] is None
            assert result["message"] == "邮箱未注册"

    @pytest.mark.asyncio
    async def test_empty_result(self):
        """测试空结果"""
        with patch('routes.email.get_emails_async', new_callable=AsyncMock) as mock_async:
            mock_async.return_value = []
            
            result = await get_emails_core("<EMAIL>", 1)
            
            assert result["status"] is False
            assert result["data"] == []
            assert "没有获取到邮件数据" in result["message"]

    @pytest.mark.asyncio
    async def test_time_formatting_error(self):
        """测试时间格式化错误处理"""
        mock_emails = [
            {
                "From": "<EMAIL>",
                "Subject": "Test",
                "Time": "invalid_timestamp"
            }
        ]
        
        with patch('routes.email.get_emails_async', new_callable=AsyncMock) as mock_async:
            mock_async.return_value = mock_emails
            
            result = await get_emails_core("<EMAIL>", 1)
            
            assert result["status"] is True
            assert result["data"][0]["FormatTime"] == "时间格式错误"

    @pytest.mark.asyncio
    async def test_timestamp_overflow(self):
        """测试时间戳溢出处理"""
        mock_emails = [
            {
                "From": "<EMAIL>",
                "Subject": "Test", 
                "Time": "999999999999999999999"  # 超大时间戳
            }
        ]
        
        with patch('routes.email.get_emails_async', new_callable=AsyncMock) as mock_async:
            mock_async.return_value = mock_emails
            
            result = await get_emails_core("<EMAIL>", 1)
            
            assert result["status"] is True
            # 应该尝试毫秒级转换或显示溢出错误
            assert result["data"][0]["FormatTime"] in ["时间戳溢出", "时间格式错误"]

    @pytest.mark.asyncio
    async def test_millisecond_timestamp(self):
        """测试毫秒级时间戳处理"""
        # 毫秒级时间戳 (2022-01-01 00:00:00 UTC * 1000)
        mock_emails = [
            {
                "From": "<EMAIL>",
                "Subject": "Test",
                "Time": "1640995200000"
            }
        ]
        
        with patch('routes.email.get_emails_async', new_callable=AsyncMock) as mock_async:
            mock_async.return_value = mock_emails
            
            result = await get_emails_core("<EMAIL>", 1)
            
            assert result["status"] is True
            # 应该能正确处理毫秒级时间戳
            assert result["data"][0]["FormatTime"] != "时间戳溢出"

    @pytest.mark.asyncio
    async def test_exception_handling(self):
        """测试异常处理"""
        with patch('routes.email.get_emails_async', new_callable=AsyncMock) as mock_async, \
             patch('routes.email.get_emails') as mock_sync:
            
            # 两个方法都抛出异常
            mock_async.side_effect = Exception("Async error")
            mock_sync.side_effect = Exception("Sync error")
            
            result = await get_emails_core("<EMAIL>", 1)
            
            assert result["status"] is False
            assert result["data"] is None
            assert "邮件获取失败" in result["message"]

    @pytest.mark.asyncio
    async def test_with_optional_parameters(self):
        """测试带可选参数的调用"""
        mock_emails = [{"From": "<EMAIL>", "Subject": "Test", "Time": "1640995200"}]
        
        with patch('routes.email.get_emails_async', new_callable=AsyncMock) as mock_async:
            mock_async.return_value = mock_emails
            
            result = await get_emails_core(
                email="<EMAIL>",
                num=1,
                username="test_user",
                request_host="api.example.com"
            )
            
            assert result["status"] is True
            mock_async.assert_called_once_with(
                "<EMAIL>", 1, "test_user", "api.example.com"
            )


# 运行测试的辅助函数
async def run_manual_tests():
    """手动运行测试（不依赖pytest）"""
    test_instance = TestEmailCore()
    
    print("运行邮件核心函数测试...")
    
    try:
        await test_instance.test_successful_email_retrieval()
        print("✓ 成功获取邮件测试通过")
    except Exception as e:
        print(f"✗ 成功获取邮件测试失败: {e}")
    
    try:
        await test_instance.test_unregistered_email()
        print("✓ 未注册邮箱测试通过")
    except Exception as e:
        print(f"✗ 未注册邮箱测试失败: {e}")
    
    try:
        await test_instance.test_empty_result()
        print("✓ 空结果测试通过")
    except Exception as e:
        print(f"✗ 空结果测试失败: {e}")
    
    print("测试完成!")


if __name__ == "__main__":
    # 如果直接运行此文件，执行手动测试
    asyncio.run(run_manual_tests())
